package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"shopifyordergo/bot"
	"shopifyordergo/config"
	"shopifyordergo/pusher"
)

func main() {
	// 加载配置
	cfg, err := config.LoadEnvConfig(".env")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 验证配置
	if cfg.ClientID == "" || cfg.ClientSecret == "" {
		log.Fatal("Client ID 和 Client Secret 不能为空")
	}

	// 加载ID管理器
	idManager, err := config.LoadIDManager("dingdingid.yaml")
	if err != nil {
		log.Fatalf("加载ID管理器失败: %v", err)
	}

	// 创建机器人实例
	dingBot := bot.NewDingTalkBot(cfg, idManager)

	// 创建消息推送器
	messagePusher, err := pusher.NewMessagePusher(cfg, idManager)
	if err != nil {
		log.Fatalf("创建消息推送器失败: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动机器人
	go func() {
		if err := dingBot.Start(ctx); err != nil {
			log.Fatalf("启动机器人失败: %v", err)
		}
	}()

	// 启动交互式命令行界面
	go startInteractiveMode(messagePusher, idManager)

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Println("钉钉机器人已启动，按 Ctrl+C 退出")
	log.Println("输入 'help' 查看可用命令")

	<-sigChan
	log.Println("正在关闭机器人...")

	cancel()
	dingBot.Stop()

	log.Println("机器人已关闭")
}

// startInteractiveMode 启动交互式命令行模式
func startInteractiveMode(pusher *pusher.MessagePusher, idManager *config.IDManager) {
	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Print("> ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}

		handleCommand(input, pusher, idManager)
	}
}

// handleCommand 处理命令行命令
func handleCommand(input string, pusher *pusher.MessagePusher, idManager *config.IDManager) {
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return
	}

	command := strings.ToLower(parts[0])

	switch command {
	case "help":
		showHelp()

	case "status":
		showStatus(idManager)

	case "users":
		showUsers(idManager)

	case "groups":
		showGroups(idManager)

	case "send":
		if len(parts) < 4 {
			fmt.Println("用法: send <user|group> <id> <message>")
			return
		}
		handleSendCommand(parts[1], parts[2], strings.Join(parts[3:], " "), pusher)

	case "broadcast":
		if len(parts) < 3 {
			fmt.Println("用法: broadcast <users|groups> <message>")
			return
		}
		handleBroadcastCommand(parts[1], strings.Join(parts[2:], " "), pusher)

	case "reload":
		reloadIDManager(idManager)

	case "test":
		if len(parts) < 2 {
			fmt.Println("用法: test <user|group> [id]")
			fmt.Println("示例: test user 024326423122739246")
			fmt.Println("示例: test group cid7notLFWYHlJlxDwYAogb2Q==")
			return
		}
		handleTestCommand(parts[1:], pusher, idManager)

	case "exit", "quit":
		fmt.Println("正在退出...")
		os.Exit(0)

	default:
		fmt.Printf("未知命令: %s，输入 'help' 查看帮助\n", command)
	}
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println(`
钉钉机器人命令行界面

可用命令：
  help                          - 显示此帮助信息
  status                        - 显示机器人状态
  users                         - 显示已记录的用户列表
  groups                        - 显示已记录的群组列表
  send user <userID> <message>  - 向指定用户发送消息
  send group <groupID> <message> - 向指定群组发送消息
  broadcast users <message>     - 向所有用户广播消息
  broadcast groups <message>    - 向所有群组广播消息
  test user [userID]            - 测试向用户发送消息
  test group [groupID]          - 测试向群组发送消息
  reload                        - 重新加载ID管理文件
  exit/quit                     - 退出程序

示例：
  send user 123456789 Hello World
  broadcast users 系统维护通知
  test user                     # 向第一个已记录用户发送测试消息
  test group                    # 向第一个已记录群组发送测试消息
`)
}

// showStatus 显示状态信息
func showStatus(idManager *config.IDManager) {
	fmt.Printf("机器人状态:\n")
	fmt.Printf("  已记录用户数: %d\n", len(idManager.Users))
	fmt.Printf("  已记录群组数: %d\n", len(idManager.Groups))
}

// showUsers 显示用户列表
func showUsers(idManager *config.IDManager) {
	if len(idManager.Users) == 0 {
		fmt.Println("暂无已记录的用户")
		return
	}

	fmt.Println("已记录用户列表:")
	for i, user := range idManager.Users {
		fmt.Printf("  %d. %s (ID: %s) - %s\n", i+1, user.Name, user.ID, user.FirstContact)
	}
}

// showGroups 显示群组列表
func showGroups(idManager *config.IDManager) {
	if len(idManager.Groups) == 0 {
		fmt.Println("暂无已记录的群组")
		return
	}

	fmt.Println("已记录群组列表:")
	for i, group := range idManager.Groups {
		fmt.Printf("  %d. %s (ID: %s) - %s\n", i+1, group.Name, group.ID, group.FirstContact)
	}
}

// handleSendCommand 处理发送命令
func handleSendCommand(target, id, message string, pusher *pusher.MessagePusher) {
	switch strings.ToLower(target) {
	case "user":
		if err := pusher.SendTextToUser(id, message); err != nil {
			fmt.Printf("发送消息失败: %v\n", err)
		} else {
			fmt.Printf("消息已发送给用户: %s\n", id)
		}

	case "group":
		if err := pusher.SendTextToGroup(id, message, nil, false); err != nil {
			fmt.Printf("发送消息失败: %v\n", err)
		} else {
			fmt.Printf("消息已发送给群组: %s\n", id)
		}

	default:
		fmt.Println("目标类型必须是 'user' 或 'group'")
	}
}

// handleBroadcastCommand 处理广播命令
func handleBroadcastCommand(target, message string, pusher *pusher.MessagePusher) {
	switch strings.ToLower(target) {
	case "users":
		if err := pusher.BroadcastTextToAllUsers(message); err != nil {
			fmt.Printf("广播消息失败: %v\n", err)
		} else {
			fmt.Println("消息已广播给所有用户")
		}

	case "groups":
		if err := pusher.BroadcastTextToAllGroups(message, false); err != nil {
			fmt.Printf("广播消息失败: %v\n", err)
		} else {
			fmt.Println("消息已广播给所有群组")
		}

	default:
		fmt.Println("广播目标必须是 'users' 或 'groups'")
	}
}

// reloadIDManager 重新加载ID管理器
func reloadIDManager(idManager *config.IDManager) {
	newManager, err := config.LoadIDManager("dingdingid.yaml")
	if err != nil {
		fmt.Printf("重新加载ID管理文件失败: %v\n", err)
		return
	}

	// 更新当前管理器的数据
	idManager.Users = newManager.Users
	idManager.Groups = newManager.Groups

	fmt.Println("ID管理文件已重新加载")
}

// handleTestCommand 处理测试推送命令
func handleTestCommand(args []string, pusher *pusher.MessagePusher, idManager *config.IDManager) {
	if len(args) == 0 {
		fmt.Println("请指定测试类型: user 或 group")
		return
	}

	testType := strings.ToLower(args[0])

	switch testType {
	case "user":
		var targetID string
		if len(args) > 1 {
			targetID = args[1]
		} else {
			// 使用第一个已记录的用户ID
			if len(idManager.Users) == 0 {
				fmt.Println("没有已记录的用户ID，请先与机器人对话或手动指定用户ID")
				return
			}
			targetID = idManager.Users[0].ID
			fmt.Printf("使用第一个已记录的用户ID: %s (%s)\n", targetID, idManager.Users[0].Name)
		}

		testMessage := "🤖 这是一条测试消息！\n\n✅ 如果您收到这条消息，说明机器人推送功能正常工作。\n\n📅 发送时间: " + time.Now().Format("2006-01-02 15:04:05")

		fmt.Printf("正在向用户 %s 发送测试消息...\n", targetID)
		if err := pusher.SendTextToUser(targetID, testMessage); err != nil {
			fmt.Printf("❌ 发送失败: %v\n", err)
		} else {
			fmt.Printf("✅ 测试消息发送成功！\n")
		}

	case "group":
		var targetID string
		if len(args) > 1 {
			targetID = args[1]
		} else {
			// 使用第一个已记录的群组ID
			if len(idManager.Groups) == 0 {
				fmt.Println("没有已记录的群组ID，请先在群组中与机器人对话或手动指定群组ID")
				return
			}
			targetID = idManager.Groups[0].ID
			fmt.Printf("使用第一个已记录的群组ID: %s (%s)\n", targetID, idManager.Groups[0].Name)
		}

		testMessage := "🤖 这是一条测试群组消息！\n\n✅ 如果群组收到这条消息，说明机器人推送功能正常工作。\n\n📅 发送时间: " + time.Now().Format("2006-01-02 15:04:05")

		fmt.Printf("正在向群组 %s 发送测试消息...\n", targetID)
		if err := pusher.SendTextToGroup(targetID, testMessage, nil, false); err != nil {
			fmt.Printf("❌ 发送失败: %v\n", err)
		} else {
			fmt.Printf("✅ 测试消息发送成功！\n")
		}

	default:
		fmt.Printf("未知的测试类型: %s，请使用 'user' 或 'group'\n", testType)
	}
}
